<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WDA Monitor - WDA_Reg Monitor</title>
    <!-- Include all the same CSS and JS dependencies as visuals.html -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/PapaParse/5.3.0/papaparse.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>

    <style>
        .alert {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
            min-width: 300px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }

        .stat-card {
            border-radius: 10px;
            transition: all 0.3s ease;
            border: none;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            margin-bottom: 1.5rem;
        }
        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 6px 12px rgba(0,0,0,0.15);
        }
        .stat-icon {
            font-size: 2.5rem;
            opacity: 0.8;
        }
        .stat-card.total-parts { background: linear-gradient(45deg, #4158D0, #C850C0); color: white; }
        .stat-card.found-parts { background: linear-gradient(45deg, #11998e, #38ef7d); color: white; }
        .stat-card.not-found-parts { background: linear-gradient(45deg, #FF416C, #FF4B2B); color: white; }
        .stat-card.not-run-parts { background: linear-gradient(45deg, #755BEA, #FF72C0); color: white; }
        .stat-card.expired-parts { background: linear-gradient(45deg, #FF6B6B, #FF8E8E); color: white; }

        .chart-container {
            position: relative;
            min-height: 400px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
            padding: 15px;
            margin-bottom: 20px;
        }

        .filter-section {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 5px;
            margin-bottom: 20px;
        }

        .filter-wrapper {
            margin-bottom: 15px;
        }

        .select2-container {
            width: 100% !important;
        }

        .select2-selection--multiple {
            max-height: 80px;
            overflow-y: auto !important;
            overflow-x: hidden;
        }

        .loading {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(255, 255, 255, 0.8);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 9999;
        }

        .loading-spinner {
            width: 50px;
            height: 50px;
            border: 5px solid #f3f3f3;
            border-top: 5px solid #3498db;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .chart-loading {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(255, 255, 255, 0.9);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 100;
            border-radius: 8px;
        }

        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
            min-width: 300px;
            padding: 15px;
            border-radius: 4px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            display: none;
            animation: slideIn 0.3s ease-out;
        }

        @keyframes slideIn {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }

        .notification.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .notification.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .data-table-container {
            margin-top: 2rem;
            overflow-x: auto;
            max-height: 600px;
            overflow-y: auto;
        }

        .data-table {
            min-width: 1200px;
            width: 100%;
            border-collapse: separate;
            border-spacing: 0;
            background: white;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        }

        .data-table th {
            background-color: #f8f9fa;
            cursor: pointer;
            padding: 12px;
            text-align: left;
            border-bottom: 2px solid #dee2e6;
            position: sticky;
            top: 0;
            z-index: 1;
        }

        .data-table td {
            padding: 12px;
            border-bottom: 1px solid #dee2e6;
        }

        .data-table tbody tr:hover {
            background-color: #f8f9fa;
        }

        .chart-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }

        .download-btn {
            background: none;
            border: none;
            color: #6c757d;
            cursor: pointer;
            padding: 5px;
            transition: color 0.2s;
        }

        .download-btn:hover {
            color: #0d6efd;
        }

        .navbar {
            margin-bottom: 20px;
        }

        /* Enhanced visual effects */
        .chart-container {
            transition: all 0.3s ease;
            border-radius: 12px;
            overflow: hidden;
        }

        .chart-container:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }

        .stat-card {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            border-radius: 12px;
            overflow: hidden;
            position: relative;
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, rgba(255,255,255,0.1), rgba(255,255,255,0.05));
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .stat-card:hover::before {
            opacity: 1;
        }

        .stat-card:hover {
            transform: translateY(-8px) scale(1.02);
            box-shadow: 0 15px 35px rgba(0,0,0,0.2);
        }

        .stat-icon {
            transition: all 0.3s ease;
        }

        .stat-card:hover .stat-icon {
            transform: scale(1.1) rotate(5deg);
        }

        /* Loading animations */
        .chart-loading {
            background: linear-gradient(45deg, rgba(255,255,255,0.9), rgba(248,249,250,0.9));
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .chart-container {
                margin-bottom: 2rem;
            }

            .stat-card {
                margin-bottom: 1rem;
            }

            .filter-section {
                padding: 15px;
            }

            .chart-header h4 {
                font-size: 1.1rem;
            }
        }

        /* Custom scrollbar for data table */
        .data-table-container::-webkit-scrollbar {
            width: 8px;
            height: 8px;
        }

        .data-table-container::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 4px;
        }

        .data-table-container::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 4px;
        }

        .data-table-container::-webkit-scrollbar-thumb:hover {
            background: #a8a8a8;
        }

        /* Enhanced filter controls */
        .filter-wrapper {
            position: relative;
        }

        .filter-wrapper label {
            font-weight: 600;
            color: #495057;
            margin-bottom: 5px;
        }

        .select2-container--default .select2-selection--multiple {
            border: 2px solid #e9ecef;
            border-radius: 8px;
            transition: all 0.3s ease;
        }

        .select2-container--default .select2-selection--multiple:focus-within {
            border-color: #0d6efd;
            box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
        }

        /* Pulse animation for loading states */
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        .loading-pulse {
            animation: pulse 1.5s ease-in-out infinite;
        }
    </style>
</head>
<body>
    <!-- Navigation Bar -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="/">
                <i class="fas fa-chart-line me-2"></i>WDA Monitor
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/">Home</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/visuals">Visualizations</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="/wda-reg-monitor">WDA_Reg Monitor</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/import-status">Import Status</a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="navbar-text me-3">
                        <i class="fas fa-user me-1"></i>
                        <span id="userFullName">{{ user.full_name if user else 'User' }}</span>
                        <span class="badge bg-light text-dark ms-1" id="userRole">{{ user.role if user else 'Unknown' }}</span>
                    </li>
                    {% if user and user.role == 'admin' %}
                    <li class="nav-item">
                        <a class="nav-link" href="/admin/dashboard">
                            <i class="fas fa-cog me-1"></i>Admin
                        </a>
                    </li>
                    {% endif %}
                    <li class="nav-item">
                        <a class="nav-link" href="#" onclick="logout()">
                            <i class="fas fa-sign-out-alt me-1"></i>Logout
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Notification Container -->
    <div id="notification" class="notification"></div>

    <!-- Loading Spinner -->
    <div id="loading" class="loading" style="display: none;">
        <div class="loading-spinner"></div>
    </div>

    <!-- Main Content -->
    <div class="container-fluid mt-3">
        <div class="row">
            <div class="col-12">
                <h2 class="mb-4">
                    <i class="fas fa-database me-2"></i>WDA_Reg System Monitor
                    <small class="text-muted">- System-wide aggregated data analysis</small>
                </h2>
            </div>
        </div>

        <!-- Filter Section -->
        <div class="row filter-section">
            <div class="col-md-2">
                <button id="refreshData" class="btn btn-outline-primary w-100 mb-3">
                    <i class="fas fa-sync-alt"></i> Refresh Data
                </button>
                <button id="forceRefreshData" class="btn btn-outline-warning w-100 mb-3">
                    <i class="fas fa-database"></i> Force Refresh from DB
                </button>
                <button id="clearFilters" class="btn btn-outline-secondary w-100 mb-3">
                    <i class="fas fa-filter"></i> Clear All Filters
                </button>
                <button id="downloadData" class="btn btn-outline-success w-100 mb-3">
                    <i class="fas fa-download"></i> Download Data
                </button>

                <!-- Data Info Section -->
                <div class="card mt-3">
                    <div class="card-header">
                        <h6 class="mb-0">Data Information</h6>
                    </div>
                    <div class="card-body">
                        <small class="text-muted">
                            <div id="dataTimestamp">Loading...</div>
                            <div id="dataRecordCount">Loading...</div>
                            <div id="dataSource">Loading...</div>
                        </small>
                    </div>
                </div>

                <!-- Filter controls will be added here dynamically -->
                <div id="filterControls"></div>
            </div>

            <!-- Right Column - Visualizations -->
            <div class="col-md-10">
                <!-- Stats Cards -->
                <div class="row mb-4" id="statsCards">
                    <!-- Stats cards will be populated dynamically -->
                </div>

                <!-- Charts Section -->
                <div class="row">
                    <!-- Status Distribution Chart -->
                    <div class="col-md-6 chart-container">
                        <div class="chart-header">
                            <h4>Status Distribution</h4>
                            <button class="download-btn" onclick="downloadChart('statusChart')">
                                <i class="fas fa-download"></i>
                            </button>
                        </div>
                        <div class="position-relative h-100">
                            <div id="statusChart"></div>
                            <div class="chart-loading">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">Loading...</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Priority Distribution Chart -->
                    <div class="col-md-6 chart-container">
                        <div class="chart-header">
                            <h4>Priority Distribution</h4>
                            <button class="download-btn" onclick="downloadChart('priorityChart')">
                                <i class="fas fa-download"></i>
                            </button>
                        </div>
                        <div class="position-relative h-100">
                            <div id="priorityChart"></div>
                            <div class="chart-loading">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">Loading...</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Second Row Charts -->
                <div class="row mt-4">
                    <!-- Manufacturer Distribution Chart -->
                    <div class="col-md-6 chart-container">
                        <div class="chart-header">
                            <h4>Top Manufacturers</h4>
                            <button class="download-btn" onclick="downloadChart('manufacturerChart')">
                                <i class="fas fa-download"></i>
                            </button>
                        </div>
                        <div class="position-relative h-100">
                            <div id="manufacturerChart"></div>
                            <div class="chart-loading">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">Loading...</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Expired vs Active Chart -->
                    <div class="col-md-6 chart-container">
                        <div class="chart-header">
                            <h4>Expired vs Active Parts</h4>
                            <button class="download-btn" onclick="downloadChart('expiredChart')">
                                <i class="fas fa-download"></i>
                            </button>
                        </div>
                        <div class="position-relative h-100">
                            <div id="expiredChart"></div>
                            <div class="chart-loading">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">Loading...</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Timeline Chart -->
                <div class="row mt-4">
                    <div class="col-12 chart-container">
                        <div class="chart-header">
                            <h4>Activity Timeline</h4>
                            <button class="download-btn" onclick="downloadChart('timelineChart')">
                                <i class="fas fa-download"></i>
                            </button>
                        </div>
                        <div class="position-relative h-100">
                            <div id="timelineChart"></div>
                            <div class="chart-loading">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">Loading...</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Data Table Section -->
                <div class="row mt-4">
                    <div class="col-12">
                        <div class="chart-container">
                            <div class="chart-header">
                                <h4>Detailed Data Table</h4>
                                <button class="download-btn" onclick="downloadTableData()">
                                    <i class="fas fa-download"></i>
                                </button>
                            </div>
                            <div class="data-table-container">
                                <table class="data-table" id="dataTable">
                                    <thead>
                                        <tr>
                                            <th>MAN_ID</th>
                                            <th>MOD_ID</th>
                                            <th>PRTY</th>
                                            <th>STATUS</th>
                                            <th>LR_DATE</th>
                                            <th>COUNT</th>
                                            <th>Is Expired</th>
                                        </tr>
                                    </thead>
                                    <tbody id="dataTableBody">
                                        <!-- Data will be populated dynamically -->
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Global variables
        let rawData = [];
        let filteredData = [];
        let currentFilters = {};

        // Initialize the page
        $(document).ready(function() {
            loadData();
            setupEventHandlers();
        });

        // Load data from API
        async function loadData() {
            showLoading(true);
            try {
                const response = await fetch('/api/wda-reg-data');
                const result = await response.json();

                if (result.status === 'success') {
                    rawData = result.data;
                    filteredData = [...rawData];
                    updateUI();

                    // Show different message based on data source
                    const source = result.from_cache ? 'cache' : 'database';
                    showNotification(`Data loaded successfully from ${source} (${result.total_records.toLocaleString()} records)`, 'success');
                } else {
                    throw new Error(result.message || 'Failed to load data');
                }
            } catch (error) {
                console.error('Error loading data:', error);
                showNotification('Error loading data: ' + error.message, 'error');
            } finally {
                showLoading(false);
            }
        }

        // Update the entire UI
        function updateUI() {
            createFilterControls();
            updateStatsCards();
            updateCharts();
            updateDataTable();
            updateDataInfo();
        }

        // Update data information display
        function updateDataInfo() {
            if (rawData.length > 0) {
                // Get timestamp from first record if available
                const firstRecord = rawData[0];
                let timestampText = 'Unknown';

                if (firstRecord.download_timestamp) {
                    const timestamp = new Date(firstRecord.download_timestamp);
                    timestampText = timestamp.toLocaleString();
                } else if (firstRecord.download_date) {
                    timestampText = firstRecord.download_date;
                }

                $('#dataTimestamp').html(`<i class="fas fa-clock"></i> Last Updated: ${timestampText}`);
                $('#dataRecordCount').html(`<i class="fas fa-list"></i> Total Records: ${rawData.length.toLocaleString()}`);
                $('#dataSource').html(`<i class="fas fa-database"></i> Source: Memory Cache (Fast Loading)`);
            } else {
                $('#dataTimestamp').text('No data available');
                $('#dataRecordCount').text('');
                $('#dataSource').text('');
            }
        }

        // Create filter controls dynamically
        function createFilterControls() {
            const filterContainer = $('#filterControls');
            filterContainer.empty();

            // Get unique values for each filter
            const manIds = [...new Set(rawData.map(d => d.MAN_ID))].sort();
            const modIds = [...new Set(rawData.map(d => d.MOD_ID))].sort();
            const priorities = [...new Set(rawData.map(d => d.PRTY))].sort();
            const statuses = [...new Set(rawData.map(d => d.STATUS))].sort();

            // Create filter HTML
            const filtersHTML = `
                <div class="filter-wrapper">
                    <label class="form-label">Manufacturer ID</label>
                    <select class="form-select select2" id="manIdFilter" multiple>
                        ${manIds.map(id => `<option value="${id}">${id}</option>`).join('')}
                    </select>
                </div>
                <div class="filter-wrapper">
                    <label class="form-label">Module ID</label>
                    <select class="form-select select2" id="modIdFilter" multiple>
                        ${modIds.map(id => `<option value="${id}">${id}</option>`).join('')}
                    </select>
                </div>
                <div class="filter-wrapper">
                    <label class="form-label">Priority</label>
                    <select class="form-select select2" id="priorityFilter" multiple>
                        ${priorities.map(p => `<option value="${p}">${p}</option>`).join('')}
                    </select>
                </div>
                <div class="filter-wrapper">
                    <label class="form-label">Status</label>
                    <select class="form-select select2" id="statusFilter" multiple>
                        ${statuses.map(s => `<option value="${s}">${s}</option>`).join('')}
                    </select>
                </div>
                <div class="filter-wrapper">
                    <label class="form-label">Expired Status</label>
                    <select class="form-select select2" id="expiredFilter" multiple>
                        <option value="true">Expired</option>
                        <option value="false">Not Expired</option>
                    </select>
                </div>
            `;

            filterContainer.html(filtersHTML);

            // Initialize Select2
            $('.select2').select2({
                placeholder: 'Select options...',
                allowClear: true,
                width: '100%'
            });

            // Add event listeners
            $('.select2').on('change', applyFilters);
        }

        // Apply filters to data
        function applyFilters() {
            filteredData = rawData.filter(item => {
                const manIdFilter = $('#manIdFilter').val();
                const modIdFilter = $('#modIdFilter').val();
                const priorityFilter = $('#priorityFilter').val();
                const statusFilter = $('#statusFilter').val();
                const expiredFilter = $('#expiredFilter').val();

                return (
                    (!manIdFilter.length || manIdFilter.includes(String(item.MAN_ID))) &&
                    (!modIdFilter.length || modIdFilter.includes(String(item.MOD_ID))) &&
                    (!priorityFilter.length || priorityFilter.includes(item.PRTY)) &&
                    (!statusFilter.length || statusFilter.includes(item.STATUS)) &&
                    (!expiredFilter.length || expiredFilter.includes(String(item.is_expired)))
                );
            });

            updateStatsCards();
            updateCharts();
            updateDataTable();
        }

        // Update statistics cards
        function updateStatsCards() {
            const totalParts = filteredData.reduce((sum, item) => sum + item.COUNT, 0);
            const foundParts = filteredData.filter(item => item.STATUS === 'found').reduce((sum, item) => sum + item.COUNT, 0);
            const notFoundParts = filteredData.filter(item => item.STATUS === 'not found').reduce((sum, item) => sum + item.COUNT, 0);
            const notRunParts = filteredData.filter(item => item.STATUS === 'not run').reduce((sum, item) => sum + item.COUNT, 0);
            const expiredParts = filteredData.filter(item => item.is_expired === true).reduce((sum, item) => sum + item.COUNT, 0);

            // Calculate percentages
            const foundPercentage = totalParts > 0 ? ((foundParts / totalParts) * 100).toFixed(1) : 0;
            const notFoundPercentage = totalParts > 0 ? ((notFoundParts / totalParts) * 100).toFixed(1) : 0;
            const notRunPercentage = totalParts > 0 ? ((notRunParts / totalParts) * 100).toFixed(1) : 0;
            const expiredPercentage = totalParts > 0 ? ((expiredParts / totalParts) * 100).toFixed(1) : 0;

            const statsHTML = `
                <div class="col-md-2">
                    <div class="card stat-card total-parts animate__animated animate__fadeInUp">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h6 class="card-subtitle mb-2">Total Parts</h6>
                                    <h2 class="card-title mb-0 counter" data-target="${totalParts}">${totalParts.toLocaleString()}</h2>
                                    <small class="opacity-75">System-wide total</small>
                                </div>
                                <i class="fas fa-boxes stat-icon"></i>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="card stat-card found-parts animate__animated animate__fadeInUp" style="animation-delay: 0.1s;">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h6 class="card-subtitle mb-2">Found Parts</h6>
                                    <h2 class="card-title mb-0 counter" data-target="${foundParts}">${foundParts.toLocaleString()}</h2>
                                    <small class="opacity-75">${foundPercentage}% of total</small>
                                </div>
                                <i class="fas fa-check-circle stat-icon"></i>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="card stat-card not-found-parts animate__animated animate__fadeInUp" style="animation-delay: 0.2s;">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h6 class="card-subtitle mb-2">Not Found</h6>
                                    <h2 class="card-title mb-0 counter" data-target="${notFoundParts}">${notFoundParts.toLocaleString()}</h2>
                                    <small class="opacity-75">${notFoundPercentage}% of total</small>
                                </div>
                                <i class="fas fa-times-circle stat-icon"></i>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="card stat-card not-run-parts animate__animated animate__fadeInUp" style="animation-delay: 0.3s;">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h6 class="card-subtitle mb-2">Not Run</h6>
                                    <h2 class="card-title mb-0 counter" data-target="${notRunParts}">${notRunParts.toLocaleString()}</h2>
                                    <small class="opacity-75">${notRunPercentage}% of total</small>
                                </div>
                                <i class="fas fa-question-circle stat-icon"></i>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="card stat-card expired-parts animate__animated animate__fadeInUp" style="animation-delay: 0.4s;">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h6 class="card-subtitle mb-2">Expired Parts</h6>
                                    <h2 class="card-title mb-0 counter" data-target="${expiredParts}">${expiredParts.toLocaleString()}</h2>
                                    <small class="opacity-75">${expiredPercentage}% of total</small>
                                </div>
                                <i class="fas fa-exclamation-circle stat-icon"></i>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="card stat-card animate__animated animate__fadeInUp" style="animation-delay: 0.5s; background: linear-gradient(45deg, #667eea, #764ba2);">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h6 class="card-subtitle mb-2">Unique Manufacturers</h6>
                                    <h2 class="card-title mb-0">${[...new Set(filteredData.map(item => item.MAN_ID))].length}</h2>
                                    <small class="opacity-75">Active manufacturers</small>
                                </div>
                                <i class="fas fa-industry stat-icon"></i>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            $('#statsCards').html(statsHTML);

            // Add counter animation
            animateCounters();
        }

        // Animate counter numbers
        function animateCounters() {
            $('.counter').each(function() {
                const $this = $(this);
                const target = parseInt($this.data('target'));
                const duration = 1500; // 1.5 seconds
                const increment = target / (duration / 16); // 60fps
                let current = 0;

                const timer = setInterval(() => {
                    current += increment;
                    if (current >= target) {
                        current = target;
                        clearInterval(timer);
                    }
                    $this.text(Math.floor(current).toLocaleString());
                }, 16);
            });
        }

        // Update charts
        function updateCharts() {
            updateStatusChart();
            updatePriorityChart();
            updateManufacturerChart();
            updateExpiredChart();
            updateTimelineChart();
        }

        // Update status distribution chart
        function updateStatusChart() {
            const statusCounts = {};
            filteredData.forEach(item => {
                statusCounts[item.STATUS] = (statusCounts[item.STATUS] || 0) + item.COUNT;
            });

            // Define colors for different statuses
            const statusColors = {
                'found': '#2ecc71',
                'not found': '#e74c3c',
                'not run': '#f39c12'
            };

            const labels = Object.keys(statusCounts);
            const values = Object.values(statusCounts);
            const colors = labels.map(label => statusColors[label] || '#95a5a6');

            const data = [{
                values: values,
                labels: labels,
                type: 'pie',
                hole: 0.4,
                textinfo: 'label+percent+value',
                textposition: 'auto',
                hovertemplate: '<b>%{label}</b><br>' +
                              'Count: %{value:,}<br>' +
                              'Percentage: %{percent}<br>' +
                              '<extra></extra>',
                marker: {
                    colors: colors,
                    line: {
                        color: '#ffffff',
                        width: 2
                    }
                },
                textfont: {
                    size: 12,
                    color: '#ffffff'
                }
            }];

            const layout = {
                height: 350,
                margin: { t: 30, b: 30, l: 30, r: 30 },
                showlegend: true,
                legend: {
                    orientation: 'v',
                    x: 1.02,
                    y: 0.5
                },
                font: {
                    family: 'Arial, sans-serif',
                    size: 12
                },
                paper_bgcolor: 'rgba(0,0,0,0)',
                plot_bgcolor: 'rgba(0,0,0,0)'
            };

            const config = {
                responsive: true,
                displayModeBar: true,
                modeBarButtonsToRemove: ['pan2d', 'lasso2d', 'select2d', 'autoScale2d'],
                displaylogo: false
            };

            Plotly.newPlot('statusChart', data, layout, config);
        }

        // Update priority distribution chart
        function updatePriorityChart() {
            const priorityCounts = {};
            filteredData.forEach(item => {
                priorityCounts[item.PRTY] = (priorityCounts[item.PRTY] || 0) + item.COUNT;
            });

            // Sort priorities for better visualization
            const sortedPriorities = Object.keys(priorityCounts).sort();
            const values = sortedPriorities.map(priority => priorityCounts[priority]);

            // Create gradient colors based on priority
            const colors = sortedPriorities.map((priority, index) => {
                const intensity = (index + 1) / sortedPriorities.length;
                return `rgba(52, 152, 219, ${0.4 + intensity * 0.6})`;
            });

            const data = [{
                x: sortedPriorities,
                y: values,
                type: 'bar',
                text: values.map(v => v.toLocaleString()),
                textposition: 'auto',
                hovertemplate: '<b>Priority: %{x}</b><br>' +
                              'Count: %{y:,}<br>' +
                              '<extra></extra>',
                marker: {
                    color: colors,
                    line: {
                        color: '#2980b9',
                        width: 1
                    }
                }
            }];

            const layout = {
                height: 350,
                margin: { t: 30, b: 80, l: 60, r: 30 },
                xaxis: {
                    title: {
                        text: 'Priority',
                        font: { size: 14, color: '#2c3e50' }
                    },
                    tickangle: -45,
                    tickfont: { size: 12 }
                },
                yaxis: {
                    title: {
                        text: 'Count',
                        font: { size: 14, color: '#2c3e50' }
                    },
                    tickformat: ',',
                    tickfont: { size: 12 }
                },
                font: {
                    family: 'Arial, sans-serif'
                },
                paper_bgcolor: 'rgba(0,0,0,0)',
                plot_bgcolor: 'rgba(0,0,0,0)',
                bargap: 0.3
            };

            const config = {
                responsive: true,
                displayModeBar: true,
                modeBarButtonsToRemove: ['pan2d', 'lasso2d', 'select2d', 'autoScale2d'],
                displaylogo: false
            };

            Plotly.newPlot('priorityChart', data, layout, config);
        }

        // Update timeline chart
        function updateTimelineChart() {
            const timelineCounts = {};
            const statusTimeline = {};

            filteredData.forEach(item => {
                if (item.LR_DATE && item.LR_DATE !== 'NaT' && item.LR_DATE !== 'null') {
                    const date = item.LR_DATE.split(' ')[0]; // Get date part only
                    const status = item.STATUS;

                    if (!timelineCounts[date]) {
                        timelineCounts[date] = 0;
                        statusTimeline[date] = {};
                    }

                    timelineCounts[date] += item.COUNT;
                    statusTimeline[date][status] = (statusTimeline[date][status] || 0) + item.COUNT;
                }
            });

            const sortedDates = Object.keys(timelineCounts).sort();

            if (sortedDates.length === 0) {
                // Show empty state
                const layout = {
                    height: 350,
                    margin: { t: 30, b: 50, l: 60, r: 30 },
                    annotations: [{
                        text: 'No timeline data available',
                        xref: 'paper',
                        yref: 'paper',
                        x: 0.5,
                        y: 0.5,
                        xanchor: 'center',
                        yanchor: 'middle',
                        showarrow: false,
                        font: { size: 16, color: '#7f8c8d' }
                    }],
                    xaxis: { visible: false },
                    yaxis: { visible: false },
                    paper_bgcolor: 'rgba(0,0,0,0)',
                    plot_bgcolor: 'rgba(0,0,0,0)'
                };
                Plotly.newPlot('timelineChart', [], layout, {responsive: true});
                return;
            }

            // Create traces for each status
            const traces = [];
            const statusColors = {
                'found': '#2ecc71',
                'not found': '#e74c3c',
                'not run': '#f39c12'
            };

            // Total count line
            traces.push({
                x: sortedDates,
                y: sortedDates.map(date => timelineCounts[date]),
                type: 'scatter',
                mode: 'lines+markers',
                name: 'Total Count',
                line: {
                    color: '#3498db',
                    width: 3,
                    shape: 'spline'
                },
                marker: {
                    color: '#2980b9',
                    size: 8,
                    line: { color: '#ffffff', width: 2 }
                },
                hovertemplate: '<b>%{x}</b><br>' +
                              'Total Count: %{y:,}<br>' +
                              '<extra></extra>'
            });

            // Status-specific lines
            Object.keys(statusColors).forEach(status => {
                const statusData = sortedDates.map(date =>
                    statusTimeline[date] && statusTimeline[date][status] ? statusTimeline[date][status] : 0
                );

                if (statusData.some(val => val > 0)) {
                    traces.push({
                        x: sortedDates,
                        y: statusData,
                        type: 'scatter',
                        mode: 'lines',
                        name: status.charAt(0).toUpperCase() + status.slice(1),
                        line: {
                            color: statusColors[status],
                            width: 2,
                            dash: 'dot'
                        },
                        hovertemplate: '<b>%{x}</b><br>' +
                                      status.charAt(0).toUpperCase() + status.slice(1) + ': %{y:,}<br>' +
                                      '<extra></extra>'
                    });
                }
            });

            const layout = {
                height: 350,
                margin: { t: 30, b: 80, l: 60, r: 30 },
                xaxis: {
                    title: {
                        text: 'Date',
                        font: { size: 14, color: '#2c3e50' }
                    },
                    tickangle: -45,
                    tickfont: { size: 10 },
                    type: 'category'
                },
                yaxis: {
                    title: {
                        text: 'Count',
                        font: { size: 14, color: '#2c3e50' }
                    },
                    tickformat: ',',
                    tickfont: { size: 12 }
                },
                font: {
                    family: 'Arial, sans-serif'
                },
                paper_bgcolor: 'rgba(0,0,0,0)',
                plot_bgcolor: 'rgba(0,0,0,0)',
                legend: {
                    orientation: 'h',
                    x: 0.5,
                    xanchor: 'center',
                    y: -0.2
                },
                hovermode: 'x unified'
            };

            const config = {
                responsive: true,
                displayModeBar: true,
                modeBarButtonsToRemove: ['pan2d', 'lasso2d', 'select2d', 'autoScale2d'],
                displaylogo: false
            };

            Plotly.newPlot('timelineChart', traces, layout, config);
        }

        // Update manufacturer distribution chart
        function updateManufacturerChart() {
            const manufacturerCounts = {};
            filteredData.forEach(item => {
                manufacturerCounts[item.MAN_ID] = (manufacturerCounts[item.MAN_ID] || 0) + item.COUNT;
            });

            // Get top 10 manufacturers
            const sortedManufacturers = Object.entries(manufacturerCounts)
                .sort(([,a], [,b]) => b - a)
                .slice(0, 10);

            const data = [{
                x: sortedManufacturers.map(([name, count]) => name),
                y: sortedManufacturers.map(([name, count]) => count),
                type: 'bar',
                text: sortedManufacturers.map(([name, count]) => count.toLocaleString()),
                textposition: 'auto',
                hovertemplate: '<b>Manufacturer: %{x}</b><br>' +
                              'Count: %{y:,}<br>' +
                              '<extra></extra>',
                marker: {
                    color: sortedManufacturers.map((_, index) => {
                        const intensity = (sortedManufacturers.length - index) / sortedManufacturers.length;
                        return `rgba(155, 89, 182, ${0.4 + intensity * 0.6})`;
                    }),
                    line: {
                        color: '#8e44ad',
                        width: 1
                    }
                }
            }];

            const layout = {
                height: 350,
                margin: { t: 30, b: 80, l: 60, r: 30 },
                xaxis: {
                    title: {
                        text: 'Manufacturer ID',
                        font: { size: 14, color: '#2c3e50' }
                    },
                    tickangle: -45,
                    tickfont: { size: 10 }
                },
                yaxis: {
                    title: {
                        text: 'Count',
                        font: { size: 14, color: '#2c3e50' }
                    },
                    tickformat: ',',
                    tickfont: { size: 12 }
                },
                font: {
                    family: 'Arial, sans-serif'
                },
                paper_bgcolor: 'rgba(0,0,0,0)',
                plot_bgcolor: 'rgba(0,0,0,0)',
                bargap: 0.3
            };

            const config = {
                responsive: true,
                displayModeBar: true,
                modeBarButtonsToRemove: ['pan2d', 'lasso2d', 'select2d', 'autoScale2d'],
                displaylogo: false
            };

            Plotly.newPlot('manufacturerChart', data, layout, config);
        }

        // Update expired vs active chart
        function updateExpiredChart() {
            const expiredCount = filteredData.filter(item => item.is_expired === true).reduce((sum, item) => sum + item.COUNT, 0);
            const activeCount = filteredData.filter(item => item.is_expired === false).reduce((sum, item) => sum + item.COUNT, 0);

            const data = [{
                values: [activeCount, expiredCount],
                labels: ['Active', 'Expired'],
                type: 'pie',
                hole: 0.4,
                textinfo: 'label+percent+value',
                textposition: 'auto',
                hovertemplate: '<b>%{label}</b><br>' +
                              'Count: %{value:,}<br>' +
                              'Percentage: %{percent}<br>' +
                              '<extra></extra>',
                marker: {
                    colors: ['#27ae60', '#e67e22'],
                    line: {
                        color: '#ffffff',
                        width: 2
                    }
                },
                textfont: {
                    size: 12,
                    color: '#ffffff'
                }
            }];

            const layout = {
                height: 350,
                margin: { t: 30, b: 30, l: 30, r: 30 },
                showlegend: true,
                legend: {
                    orientation: 'v',
                    x: 1.02,
                    y: 0.5
                },
                font: {
                    family: 'Arial, sans-serif',
                    size: 12
                },
                paper_bgcolor: 'rgba(0,0,0,0)',
                plot_bgcolor: 'rgba(0,0,0,0)'
            };

            const config = {
                responsive: true,
                displayModeBar: true,
                modeBarButtonsToRemove: ['pan2d', 'lasso2d', 'select2d', 'autoScale2d'],
                displaylogo: false
            };

            Plotly.newPlot('expiredChart', data, layout, config);
        }

        // Update data table
        function updateDataTable() {
            const tableBody = $('#dataTableBody');
            tableBody.empty();

            filteredData.forEach(item => {
                const row = `
                    <tr>
                        <td>${item.MAN_ID}</td>
                        <td>${item.MOD_ID}</td>
                        <td>${item.PRTY}</td>
                        <td>${item.STATUS}</td>
                        <td>${item.LR_DATE || '-'}</td>
                        <td>${item.COUNT.toLocaleString()}</td>
                        <td>${item.is_expired ? 'Yes' : 'No'}</td>
                    </tr>
                `;
                tableBody.append(row);
            });
        }

        // Force refresh data from database
        async function forceRefreshData() {
            showLoading(true);
            try {
                const response = await fetch('/api/refresh-wda-reg-data', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                const result = await response.json();

                if (result.status === 'success') {
                    showNotification('Data refreshed from database successfully', 'success');
                    // Reload the data after refresh
                    await loadData();
                } else {
                    throw new Error(result.message || 'Failed to refresh data from database');
                }
            } catch (error) {
                console.error('Error force refreshing data:', error);
                showNotification('Error refreshing data from database: ' + error.message, 'error');
            } finally {
                showLoading(false);
            }
        }

        // Setup event handlers
        function setupEventHandlers() {
            $('#refreshData').on('click', loadData);
            $('#forceRefreshData').on('click', forceRefreshData);
            $('#clearFilters').on('click', clearAllFilters);
            $('#downloadData').on('click', downloadTableData);
        }

        // Clear all filters
        function clearAllFilters() {
            $('.select2').val(null).trigger('change');
            filteredData = [...rawData];
            updateStatsCards();
            updateCharts();
            updateDataTable();
        }

        // Download table data as CSV
        function downloadTableData() {
            const csvContent = convertToCSV(filteredData);
            const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');
            const url = URL.createObjectURL(blob);
            link.setAttribute('href', url);
            link.setAttribute('download', 'wda_reg_data.csv');
            link.style.visibility = 'hidden';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }

        // Convert data to CSV format
        function convertToCSV(data) {
            const headers = ['MAN_ID', 'MOD_ID', 'PRTY', 'CS', 'LRD2', 'V_NOTFOUND_DAT2', 'STATUS', 'LR_DATE', 'COUNT', 'is_expired'];
            const csvRows = [headers.join(',')];

            data.forEach(item => {
                const row = [
                    item.MAN_ID,
                    item.MOD_ID,
                    item.PRTY,
                    item.CS,
                    item.LRD2 || '',
                    item.V_NOTFOUND_DAT2 || '',
                    item.STATUS,
                    item.LR_DATE || '',
                    item.COUNT,
                    item.is_expired
                ];
                csvRows.push(row.join(','));
            });

            return csvRows.join('\n');
        }

        // Download chart as image
        function downloadChart(chartId) {
            Plotly.downloadImage(chartId, {
                format: 'png',
                width: 800,
                height: 600,
                filename: chartId
            });
        }

        // Show/hide loading spinner
        function showLoading(show) {
            if (show) {
                $('#loading').show();
            } else {
                $('#loading').hide();
            }
        }

        // Show notification
        function showNotification(message, type) {
            const notification = $('#notification');
            notification.removeClass('success error').addClass(type);
            notification.text(message);
            notification.show();

            setTimeout(() => {
                notification.hide();
            }, 5000);
        }

        // Logout function
        function logout() {
            fetch('/logout', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success') {
                    window.location.href = '/login';
                }
            })
            .catch(error => {
                console.error('Error during logout:', error);
                window.location.href = '/login';
            });
        }
    </script>
</body>
</html>
